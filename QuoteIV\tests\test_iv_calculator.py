#!/usr/bin/python
# -*- coding:utf-8 -*-
# Author: ltw
# Time: 2025-02-07

import unittest
import logging
from volatility.iv_calculator import IVCalculator
from volatility.iv_manager import ContractManager, MarketDataManager, SubscriptionManager


class TestIVCalculator(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger('test_iv_calculator')

        # 初始化依赖的管理器
        cls.contract_manager = ContractManager(logger)
        cls.market_data_manager = MarketDataManager(logger)
        cls.subscription_manager = SubscriptionManager(logger)

        # 初始化计算器
        cls.calculator = IVCalculator(
            contract_manager=cls.contract_manager,
            market_data_manager=cls.market_data_manager,
            subscription_manager=cls.subscription_manager,
            logger=logger,
            active_contracts_file=None  # 测试时不使用活跃合约配置
        )

    def test_calculate_iv_normal_call(self):
        """测试正常情况下看涨期权的隐含波动率计算"""
        # 使用Black-Scholes公式已知的测试数据
        # S=100, K=100, T=1, r=0.03, sigma=0.2 的看涨期权价格约为 10.45
        iv = self.calculator.calculate_iv(
            S=100.0,  # 标的价格
            K=100.0,  # 行权价
            T=1.0,  # 剩余期限（1年）
            r=0.03,  # 无风险利率
            option_price=10.45,  # 期权价格
            option_type='call'
        )
        self.assertAlmostEqual(iv, 0.2, places=3)

    def test_calculate_iv_normal_put(self):
        """测试正常情况下看跌期权的隐含波动率计算"""
        # 使用Black-Scholes公式已知的测试数据
        # S=100, K=100, T=1, r=0.03, sigma=0.2 的看跌期权价格约为 7.83
        iv = self.calculator.calculate_iv(
            S=100.0,
            K=100.0,
            T=1.0,
            r=0.03,
            option_price=7.83,
            option_type='put'
        )
        self.assertAlmostEqual(iv, 0.2, places=3)

    def test_calculate_iv_itm_call(self):
        """测试实值看涨期权"""
        # S=120, K=100, T=1, r=0.03, sigma=0.3 的看涨期权
        iv = self.calculator.calculate_iv(
            S=120.0,
            K=100.0,
            T=1.0,
            r=0.03,
            option_price=25.93,
            option_type='call'
        )
        self.assertAlmostEqual(iv, 0.3, places=3)

    def test_calculate_iv_otm_put(self):
        """测试虚值看跌期权"""
        # S=120, K=100, T=1, r=0.03, sigma=0.3 的看跌期权
        iv = self.calculator.calculate_iv(
            S=120.0,
            K=100.0,
            T=1.0,
            r=0.03,
            option_price=3.85,
            option_type='put'
        )
        self.assertAlmostEqual(iv, 0.3, places=3)

    def test_calculate_iv_short_expiry(self):
        """测试短期限期权"""
        # 测试剩余期限为1个月的期权
        iv = self.calculator.calculate_iv(
            S=100.0,
            K=100.0,
            T=1 / 12,  # 1个月
            r=0.03,
            option_price=2.85,
            option_type='call'
        )
        self.assertTrue(0 < iv < 3.0)  # 确保结果在合理范围内

    def test_calculate_iv_edge_cases(self):
        """测试边界情况"""
        # 测试零期权价格
        iv = self.calculator.calculate_iv(
            S=100.0,
            K=100.0,
            T=1.0,
            r=0.03,
            option_price=0.0,
            option_type='call'
        )
        self.assertEqual(iv, 0.0)

        # 测试零剩余期限
        iv = self.calculator.calculate_iv(
            S=100.0,
            K=100.0,
            T=0.0,
            r=0.03,
            option_price=10.0,
            option_type='call'
        )
        self.assertEqual(iv, 0.0)

    def test_calculate_iv_invalid_inputs(self):
        """测试无效输入"""
        # 测试负的期权价格
        iv = self.calculator.calculate_iv(
            S=100.0,
            K=100.0,
            T=1.0,
            r=0.03,
            option_price=-1.0,
            option_type='call'
        )
        self.assertEqual(iv, 0.0)

        # 测试负的剩余期限
        iv = self.calculator.calculate_iv(
            S=100.0,
            K=100.0,
            T=-1.0,
            r=0.03,
            option_price=10.0,
            option_type='call'
        )
        self.assertEqual(iv, 0.0)

    def test_calculate_iv_extreme_values(self):
        """测试极端值"""
        # 测试极高的期权价格
        iv = self.calculator.calculate_iv(
            S=100.0,
            K=100.0,
            T=1.0,
            r=0.03,
            option_price=1000.0,
            option_type='call'
        )
        self.assertLessEqual(iv, 3.0)  # 确保不超过300%的波动率上限

        # 测试极低的期权价格（但大于0）
        iv = self.calculator.calculate_iv(
            S=100.0,
            K=100.0,
            T=1.0,
            r=0.03,
            option_price=0.001,
            option_type='call'
        )
        self.assertGreaterEqual(iv, 0.001)  # 确保不低于0.1%的波动率下限


if __name__ == '__main__':
    unittest.main()
