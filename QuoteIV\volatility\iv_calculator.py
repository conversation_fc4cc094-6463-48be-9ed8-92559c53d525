#!/usr/bin/python
# -*- coding:utf-8 -*-
# Author: ltw
# Time: 2025-01-29

import json
import multiprocessing
import os
import re
import sched
import threading
import time
import traceback
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from queue import Queue
from typing import Dict, Optional, List, Tuple

import numpy as np
import pandas as pd
from scipy.interpolate import CubicSpline
from scipy.stats import norm

from volatility.iv_manager import ContractManager, MarketDataManager, SubscriptionManager
from volatility.logger_utils import ErrorLoggerAdapter
from volatility.quote_recommender import QuoteRecommender
from volatility.spline_fitter import IVSplineFitter, MoneynessFitPoint
from volatility.time_utils import calculate_time_to_expiry


@dataclass
class IVResult:
    """隐含波动率计算结果"""
    product_code: str  # 品种代码
    contract_id: str  # 合约代码
    underlying: str  # 标的合约
    strike_price: float  # 行权价
    option_type: str  # 期权类型
    expire_date: str  # 到期日
    remain_time: float  # 剩余时间
    bid_iv: float  # bid隐含波动率
    ask_iv: float  # ask隐含波动率
    timestamp: float  # 计算时间戳
    underlying_price: float  # 标的价格（中间价）
    moneyness: float  # 虚实度(K/S)
    fitted_iv: Optional[float] = None  # 拟合后的隐含波动率


@dataclass
class InterpolatedIVPoint:
    """插值波动率点"""
    strike_price: float  # 行权价
    moneyness: float  # 虚实度
    remain_time: float  # 剩余期限（天）
    fitted_iv: float  # 拟合波动率
    bid_iv: float  # bid波动率
    ask_iv: float  # ask波动率
    vol_std: float  # 波动率标准差
    expire_date: str  # 到期日
    underlying_price: float  # 标的价格
    is_interpolated: bool = True  # 是否为插值点


@dataclass
class SplineFitResult:
    """样条拟合结果"""
    underlying: str  # 标的合约
    option_type: str  # 期权类型
    strikes: List[float]  # 行权价列表
    moneyness: List[float]  # 虚实度列表
    ivs: List[float]  # 原始隐含波动率列表
    ivs_bid: List[float]  # bid隐含波动率列表
    ivs_ask: List[float]  # ask隐含波动率列表
    spline: Optional[CubicSpline]  # 拟合的样条对象
    spline_bid: Optional[CubicSpline]  # bid拟合的样条对象
    spline_ask: Optional[CubicSpline]  # ask拟合的样条对象
    interpolated_points: Dict[str, InterpolatedIVPoint] = field(default_factory=dict)  # 插值点结果, 按"执行价_到期日"索引


class IVCalculator:
    """隐含波动率计算器"""

    def __init__(self, contract_manager: ContractManager, market_data_manager: MarketDataManager,
                 subscription_manager: SubscriptionManager, logger: ErrorLoggerAdapter,
                 trading_dates_file: str = 'trading_date.csv', contract_info_file: str = 'contract_info.csv',
                 option_info_file: str = 'option_infos.csv', active_contracts_file: str = None,
                 enable_spline_fit: bool = False, min_points_for_fit: int = 4) -> None:
        """
        初始化隐含波动率计算器

        Args:
            contract_manager: 合约管理器
            market_data_manager: 行情数据管理器
            subscription_manager: 订阅管理器
            logger: 日志记录器
            trading_dates_file: 交易日历文件路径
            contract_info_file: 合约信息文件路径
            option_info_file: 期权信息文件路径
            enable_spline_fit: 是否启用样条拟合
            min_points_for_fit: 拟合所需的最小数据点数量
            active_contracts_file: 活跃合约配置文件路径
        """
        self.contract_manager = contract_manager
        self.market_data_manager = market_data_manager
        self.subscription_manager = subscription_manager
        self.logger = logger
        self.enable_spline_fit = enable_spline_fit
        self.min_points_for_fit = min_points_for_fit
        self.trading_dates_file = trading_dates_file

        # 设置活跃合约配置文件路径
        if active_contracts_file is None:
            # 默认路径为 web/backend/active_contracts.json
            self.active_contracts_file = os.path.join(os.getcwd(), 'web', 'backend', 'active_contracts.json')
        else:
            self.active_contracts_file = os.path.join(os.getcwd(), 'web', 'backend', active_contracts_file)
        self.active_contracts_config = self.load_active_contracts_config()

        # 读取交易日历
        try:
            self.trading_dates = pd.read_csv(os.path.join(os.getcwd(), '..', trading_dates_file), parse_dates=[0])
            self.logger.info(f"成功读取交易日历文件: {trading_dates_file}")
        except Exception as e:
            raise Exception(f"读取交易日历文件失败: {str(e)}")

        # 读取合约信息
        try:
            self.contract_info = pd.read_csv(os.path.join(os.getcwd(), '..', contract_info_file), index_col=0)
            self.logger.info(f"成功读取合约信息文件: {contract_info_file}")
        except Exception as e:
            raise Exception(f"读取合约信息文件失败: {str(e)}")

        self.load_history_data()

        self._spline_fitter = IVSplineFitter(self.logger)

        # 计算线程池
        self.executor = ThreadPoolExecutor(max_workers=max(1, (multiprocessing.cpu_count() - 1)))
        self.calculation_threads: Dict[str, threading.Event] = {}  # 品种计算控制
        self.calculation_queues: Dict[str, Queue] = {}  # 品种计算队列

        # 计算结果缓存
        self._lock = threading.Lock()
        self.iv_results: Dict[str, Dict[str, IVResult]] = {}  # 按品种存储计算结果
        self.atm_iv_results: Dict[str, Tuple[str, float]] = {}  # 按合约存储ATM期权计算结果
        self.spline_results: Dict[str, Dict[str, Dict[str, SplineFitResult]]] = {}

        # 初始化报价推荐器
        self.quote_recommender = QuoteRecommender(
            self.logger,
            trading_dates_file=trading_dates_file,
            contract_info_file=contract_info_file,
            option_info_file=option_info_file,
            vol_diff_data=self.vol_diff_data,
            vol_diff_cont_months=self.vol_diff_cont_months,
            vol_newest=self.vol_newest
        )

        # ATM IV计算控制
        self.atm_iv_running = False
        self.atm_iv_thread = None

        # 启动清理线程
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()

        self.logger.info("IVCalculator初始化完成")

    def load_active_contracts_config(self) -> Dict[str, List[str]]:
        """
        读取活跃合约配置文件

        Returns:
            Dict[str, List[str]]: 品种代码到活跃合约列表的映射
        """
        try:
            if not os.path.exists(self.active_contracts_file):
                self.logger.warning(f"活跃合约配置文件不存在: {self.active_contracts_file}")
                return {}

            with open(self.active_contracts_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 提取活跃合约信息
            active_contracts = {}
            for product_code, product_config in config_data.items():
                if isinstance(product_config, dict) and 'active' in product_config:
                    active_list = product_config['active']
                    if isinstance(active_list, list) and len(active_list) > 0:
                        active_contracts[product_code.upper()] = active_list
                        self.logger.debug(f"品种 {product_code} 的活跃合约: {active_list}")
                    else:
                        self.logger.debug(f"品种 {product_code} 没有配置活跃合约或活跃合约为空")
                elif isinstance(product_config, list) and len(product_config) > 0:
                    # 兼容旧格式（直接是合约列表）
                    active_contracts[product_code.upper()] = product_config
                    self.logger.debug(f"品种 {product_code} 的活跃合约（旧格式）: {product_config}")

            self.logger.info(f"成功读取活跃合约配置，共 {len(active_contracts)} 个品种有活跃合约配置")
            return active_contracts

        except Exception as e:
            self.logger.error(f"读取活跃合约配置文件失败: {str(e)}")
            return {}

    def filter_contracts_by_active(self, contracts: Dict, product_code: str,
                                   active_contracts: Dict[str, List[str]]) -> Dict:
        """
        根据活跃合约配置过滤期权合约

        Args:
            contracts: 原始合约信息
            product_code: 品种代码
            active_contracts: 活跃合约配置

        Returns:
            Dict: 过滤后的合约信息
        """
        product_upper = product_code.upper()

        # 如果该品种没有配置活跃合约或活跃合约为空，返回全部合约
        if product_upper not in active_contracts or not active_contracts[product_upper]:
            self.logger.debug(f"品种 {product_code} 没有活跃合约配置，使用全部合约进行拟合")
            return contracts

        active_list = active_contracts[product_upper]
        self.logger.info(f"品种 {product_code} 使用活跃合约进行拟合: {active_list}")

        # 创建过滤后的合约结构
        filtered_contracts = {
            'futures': {},
            'options': {
                'call': {},
                'put': {}
            }
        }

        # 过滤期货合约
        for contract_id, contract_info in contracts.get('futures', {}).items():
            if contract_id in active_list:
                filtered_contracts['futures'][contract_id] = contract_info

        # 过滤期权合约（只保留标的合约在活跃列表中的期权）
        for option_type in ['call', 'put']:
            for strike_price, options in contracts.get('options', {}).get(option_type, {}).items():
                filtered_options = {}
                for contract_id, contract_info in options.items():
                    if contract_info.underlying in active_list:
                        filtered_options[contract_id] = contract_info

                if filtered_options:
                    filtered_contracts['options'][option_type][strike_price] = filtered_options

        # 统计过滤结果
        original_option_count = sum(len(strike) for opt_type in contracts.get('options', {}).values()
                                    for strike in opt_type.values())
        filtered_option_count = sum(len(strike) for opt_type in filtered_contracts['options'].values()
                                    for strike in opt_type.values())

        self.logger.info(f"品种 {product_code} 合约过滤结果: 原始期权合约 {original_option_count} 个, "
                         f"过滤后期权合约 {filtered_option_count} 个")

        return filtered_contracts

    def load_history_data(self) -> None:
        # 读取波动率差异统计数据
        try:
            self.vol_diff_data = {}
            self.vol_diff_cont_months = {}
            diff_xlsx = pd.ExcelFile(os.path.join(os.getcwd(), 'output', 'volatility_differences.xlsx'))
            self.vol_newest = pd.read_excel(os.path.join(os.getcwd(), 'output', 'historical_volatilities.xlsx'),
                                            sheet_name=None)

            # 读取所有符合命名规则的sheet
            pattern = r'stats_([A-Za-z]+)_(\d+)_(\d+)'
            for sheet_name in diff_xlsx.sheet_names:
                match = re.match(pattern, sheet_name)
                if match:
                    product = match.group(1).upper()
                    first_month = match.group(2)
                    second_month = match.group(3)
                    if product not in self.vol_diff_cont_months:
                        self.vol_diff_cont_months[product] = [first_month, second_month]
                    else:
                        if first_month not in self.vol_diff_cont_months[product]:
                            self.vol_diff_cont_months[product].append(first_month)
                        if second_month not in self.vol_diff_cont_months[product]:
                            self.vol_diff_cont_months[product].append(second_month)

                    df = pd.read_excel(diff_xlsx, sheet_name, index_col=0)
                    if 'vol_diff_20d' in df.index and pd.notna(df.loc['vol_diff_20d', 'std']):
                        self.vol_diff_data[(product, first_month, second_month)] = df.loc['vol_diff_20d', 'std']  # noqa

            for product, months in self.vol_diff_cont_months.items():
                self.vol_diff_cont_months[product] = sorted(months)

            self.logger.info("成功读取波动率差异统计数据")
        except Exception as e:
            self.logger.error(f"读取波动率差异统计数据失败: {str(e)}")
            self.vol_diff_data = {}
            self.vol_diff_cont_months = {}
            self.vol_newest = {}

    def set_quote_recommender_data(self) -> None:
        self.quote_recommender.reload_data(self.vol_diff_data, self.vol_diff_cont_months, self.vol_newest)

    def _black_scholes_price(self, S: float, K: float, T: float, r: float, sigma: float, option_type: str) -> float:
        """
        计算Black-Scholes期权价格

        Args:
            S: 标的价格
            K: 行权价
            T: 剩余期限（年）
            r: 无风险利率
            sigma: 波动率
            option_type: 期权类型 ('call' 或 'put')

        Returns:
            期权价格
        """
        if T <= 0 or sigma <= 0:
            return 0.0

        d1 = (np.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)

        if option_type == 'call':
            price = S * norm.cdf(d1) - K * np.exp(-r * T) * norm.cdf(d2)
        else:  # put
            price = K * np.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)

        return price

    def _vega(self, S: float, K: float, T: float, r: float, sigma: float) -> float:
        """
        计算Vega(期权价格对波动率的导数)
        """
        if T <= 0 or sigma <= 0:
            return 0.0

        d1 = (np.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * np.sqrt(T))
        return S * np.sqrt(T) * norm.pdf(d1)

    def calculate_iv(self, S: float, K: float, T: float, r: float, option_price: float, option_type: str) -> float:
        """
        使用牛顿迭代法计算隐含波动率

        Args:
            S: 标的价格
            K: 行权价
            T: 剩余期限（年）
            r: 无风险利率
            option_price: 期权价格
            option_type: 期权类型 ('call' 或 'put')

        Returns:
            隐含波动率
        """
        if option_price <= 0 or T <= 0:
            return 0.0

        # 设置初始猜测值和迭代参数
        sigma = 0.3  # 初始猜测值30%
        max_iterations = 100
        tolerance = 1e-5

        for i in range(max_iterations):
            # 计算当前波动率下的期权价格
            price = self._black_scholes_price(S, K, T, r, sigma, option_type)
            diff = option_price - price

            # 如果差异足够小, 返回当前波动率
            if abs(diff) < tolerance:
                self.logger.debug(f"隐含波动率计算收敛: sigma={sigma:.4f}, iterations={i + 1}")
                return sigma

            # 计算vega
            v = self._vega(S, K, T, r, sigma)

            # 防止除以零
            if abs(v) < 1e-10:
                # self.logger.warning("Vega接近零, 可能导致不稳定的计算结果")
                return 0.0

            # 牛顿迭代
            sigma = sigma + diff / v

            # 添加边界检查
            if sigma <= 0.001:  # 最小波动率0.1%
                return 0.001
            if sigma > 3:  # 最大波动率300%
                return 3.0

        return float(max(0.001, min(3.0, sigma)))

    def calculate_product_iv(self, product_code: str, stop_event: threading.Event) -> None:
        """单个品种的隐含波动率定时计算循环"""
        self.logger.info(f"开始计算品种 {product_code} 的隐含波动率")
        scheduler = sched.scheduler(time.time, time.sleep)
        scheduler_task = None  # 用于存储下一个调度任务

        def calculate_once():
            """执行一次计算"""
            nonlocal scheduler_task
            if stop_event.is_set():
                if scheduler_task:
                    try:
                        scheduler.cancel(scheduler_task)
                    except ValueError:
                        pass
                return

            self._do_calculate(product_code)

            # 安排下一次计算
            if not stop_event.is_set():
                current_time = time.time()
                next_time = (current_time // 30 + 1) * 30
                scheduler_task = scheduler.enterabs(next_time, 1, calculate_once)

        # 计算第一次执行的时间（对齐到下一个30秒的整点）
        current_time = time.time()
        next_time = (current_time // 30 + 1) * 30
        scheduler_task = scheduler.enterabs(next_time, 1, calculate_once)

        # 运行调度器
        while not stop_event.is_set():
            try:
                scheduler.run(blocking=False)
                time.sleep(0.1)
            except Exception as e:
                self.logger.error(f"调度器运行出错: {str(e)}")
                if not stop_event.is_set():
                    time.sleep(1)

        if scheduler_task:
            try:
                scheduler.cancel(scheduler_task)
            except ValueError:
                pass

        self.logger.info(f"品种 {product_code} 的计算已停止")

    def force_calculate_product_iv(self, product_code: str) -> None:
        """强制执行一次品种的隐含波动率计算"""
        self.logger.info(f"强制计算品种 {product_code} 的隐含波动率")
        self._do_calculate(product_code)

    def _do_calculate(self, product_code: str) -> None:
        """执行实际的计算逻辑"""
        start_time = time.time()
        try:
            # 获取该品种的所有合约信息
            contracts = self.contract_manager.get_product_contracts(product_code)
            if not contracts:
                return

            # 读取活跃合约配置并过滤合约
            contracts = self.filter_contracts_by_active(contracts, product_code, self.active_contracts_config)

            # 如果过滤后没有期权合约，跳过计算
            if not contracts.get('options') or not any(contracts['options'].values()):
                self.logger.warning(f"品种 {product_code} 过滤后没有可用的期权合约, 跳过计算")
                return

            results = {}

            # 获取日内分钟数
            intraday_minutes = self.contract_info.loc[product_code.upper(), 'trade_minutes']

            # 如果日内分钟数小于300分钟, 并且当前时间不在交易时间内, 则跳过计算
            now_hour = time.localtime().tm_hour
            if intraday_minutes < 300 and (now_hour > 17 or now_hour < 8):
                return

            self.logger.info(f"开始处理品种 {product_code} 的期权合约")
            success_count, error_count = 0, 0
            total_count = sum(len(strike) for opt_type in contracts['options'].values() for strike in opt_type.values())

            # 处理每个期权合约
            for option_type in ['call', 'put']:
                for strike_price, options in contracts['options'][option_type].items():
                    strike_price_float = float(strike_price)
                    for contract_id, contract_info in options.items():
                        try:
                            # 获取期权合约的市场数据
                            option_data = self.market_data_manager.get_market_data(contract_id)
                            if not option_data:
                                error_count += 1
                                continue

                            # 获取标的合约的市场数据
                            underlying_data = self.market_data_manager.get_market_data(contract_info.underlying)
                            if not underlying_data:
                                error_count += 1
                                continue

                            # 计算标的中间价
                            underlying_mid_price = (underlying_data.bid_price + underlying_data.ask_price) * 0.5
                            if (underlying_data.bid_price < 1e-4 or underlying_data.ask_price < 1e-4 or
                                    underlying_data.ask_price - underlying_data.bid_price > 2 * underlying_data.last_price):
                                underlying_mid_price = underlying_data.last_price

                            # 计算剩余期限
                            T = calculate_time_to_expiry(contract_info.expire_date, self.trading_dates,
                                                         intraday_minutes)
                            if T is None or T <= 0:
                                error_count += 1
                                continue
                            T = float(T)

                            # 计算bid/ask的隐含波动率
                            try:
                                option_mid_price = (option_data.bid_price + option_data.ask_price) * 0.5
                                if (option_data.bid_price < 1e-4 or option_data.ask_price < 1e-4 or
                                        option_data.ask_price - option_data.bid_price > 2 * option_data.last_price):
                                    option_mid_price = underlying_data.last_price
                                mid_iv = self.calculate_iv(underlying_mid_price, strike_price_float, T, 0.0,
                                                           option_mid_price, option_type
                                                           ) if underlying_mid_price > 0 else 0.0

                                bid_iv = self.calculate_iv(underlying_data.bid_price, strike_price_float, T, 0.0,
                                                           option_data.bid_price, option_type
                                                           ) if option_data.bid_price > 0 else mid_iv

                                ask_iv = self.calculate_iv(underlying_data.ask_price, strike_price_float, T, 0.0,
                                                           option_data.ask_price, option_type
                                                           ) if option_data.ask_price > 0 else mid_iv

                                # 计算虚实度
                                moneyness = strike_price_float / underlying_mid_price

                                # 保存计算结果
                                results[contract_id] = IVResult(
                                    product_code=product_code,
                                    contract_id=contract_id,
                                    underlying=contract_info.underlying,
                                    strike_price=strike_price_float,
                                    option_type=option_type,
                                    expire_date=contract_info.expire_date,
                                    remain_time=T * 244.0,
                                    bid_iv=bid_iv,
                                    ask_iv=ask_iv,
                                    timestamp=time.time(),
                                    underlying_price=underlying_mid_price,
                                    moneyness=moneyness
                                )
                                success_count += 1
                            except Exception as e:
                                self.logger.error(f"计算合约 {contract_id} 的隐含波动率时出错: {str(e)}")
                                error_count += 1

                        except Exception as e:
                            self.logger.error(f"处理合约 {contract_id} 时出错: {str(e)}")
                            error_count += 1

            # 如果启用了样条拟合, 执行拟合
            if self.enable_spline_fit and results:
                try:
                    self.logger.info(f"开始执行品种 {product_code} 的样条拟合")
                    spline_start_time = time.time()

                    # 获取品种的最小变动价位
                    tick_size = float(self.contract_info.loc[product_code.upper(), 'tick_size'])

                    # 获取所有有效的执行价和到期日
                    all_strikes = sorted(set(r.strike_price for r in results.values()))
                    all_expiries = sorted(set(r.expire_date for r in results.values()))

                    # 生成插值执行价列表
                    min_strike = min(all_strikes)
                    max_strike = max(all_strikes)
                    total_steps = int((max_strike - min_strike) / tick_size)
                    step_size = max(1, total_steps // (min(50, total_steps) - 1))
                    interpolated_strikes = [min_strike + i * step_size * tick_size
                                            for i in range(min(50, total_steps + 1))]

                    # 生成插值到期日列表
                    current_date = datetime.now().date()
                    max_expiry = max(datetime.strptime(exp, '%Y%m%d').date() for exp in all_expiries)
                    days_range = (max_expiry - current_date).days
                    num_expiries = min(50, days_range)

                    # 获取交易日历的日期范围
                    trading_dates_range = pd.to_datetime(self.trading_dates.iloc[:, 0]).dt.date
                    min_trading_date = trading_dates_range.min()
                    max_trading_date = trading_dates_range.max()

                    # 生成候选日期
                    interpolated_expiries = []
                    for i in range(num_expiries):
                        candidate_date = current_date + timedelta(days=int(i * days_range / (num_expiries - 1)))
                        # 确保日期在交易日历范围内
                        if min_trading_date <= candidate_date <= max_trading_date:
                            # 找到最近的交易日
                            trading_dates_array = trading_dates_range.to_numpy()
                            nearest_idx = np.abs(trading_dates_array - candidate_date).argmin()
                            nearest_trading_date = trading_dates_array[nearest_idx]
                            interpolated_expiries.append(nearest_trading_date.strftime('%Y-%m-%d'))

                    # 去重并排序
                    interpolated_expiries = sorted(set(interpolated_expiries))
                    self.logger.debug(f"生成插值到期日列表: {interpolated_expiries}")

                    # 执行样条拟合
                    fitted_results = self._spline_fitter.fit_iv_surface(contracts, results, self.min_points_for_fit,
                                                                        use_moneyness=True)

                    # 存储拟合和插值结果
                    spline_results = {}
                    expiry_dates = list(all_expiries)

                    # 首先对每个已有的到期日进行执行价插值
                    expiry_iv_data = {}  # 存储每个到期日的完整波动率数据
                    for underlying, type_data in fitted_results.items():
                        expiry_date = self.contract_manager.get_option_underlying_expire_date(underlying)
                        if expiry_date not in expiry_iv_data:
                            expiry_iv_data[expiry_date] = {'call': {}, 'put': {}}

                        for option_type, (_, ivs, spline) in type_data.items():
                            if not spline:
                                continue

                            # 获取该组合的所有期权
                            group_options = [r for r in results.values() if r.underlying == underlying and
                                             r.option_type == option_type]

                            if not group_options:
                                continue

                            # 获取所有有效的moneyness和对应的fitted_iv
                            valid_points = [(p.moneyness, float(spline(p.moneyness)))
                                            for p in group_options if float(spline(p.moneyness)) >= 0.005]
                            if valid_points:
                                # 按moneyness排序
                                valid_points.sort(key=lambda x: x[0])

                            # 为每个插值执行价计算波动率
                            for strike in interpolated_strikes:
                                moneyness = strike / group_options[0].underlying_price
                                try:
                                    fitted_iv = float(spline(moneyness))
                                    # 如果fitted_iv小于0.005, 尝试使用线性插值
                                    if fitted_iv < 0.005:
                                        if valid_points:
                                            # 找到最接近的两个moneyness
                                            left_idx = -1
                                            for i, (m, _) in enumerate(valid_points):
                                                if m > moneyness:
                                                    left_idx = i - 1
                                                    break

                                            if left_idx >= 0 and left_idx + 1 < len(valid_points):
                                                # 有两个点可以用于线性插值
                                                left_m, left_iv = valid_points[left_idx]
                                                right_m, right_iv = valid_points[left_idx + 1]

                                                # 线性插值
                                                weight = (moneyness - left_m) / (right_m - left_m)
                                                fitted_iv = left_iv + weight * (right_iv - left_iv)
                                            elif valid_points:
                                                # 只有一个有效点, 使用最接近的点
                                                closest_idx = min(range(len(valid_points)),
                                                                  key=lambda i: abs(valid_points[i][0] - moneyness))
                                                fitted_iv = valid_points[closest_idx][1]

                                    expiry_iv_data[expiry_date][option_type][strike] = {
                                        'moneyness': moneyness,
                                        'iv': fitted_iv
                                    }
                                except Exception as e:
                                    self.logger.error(f"计算执行价 {strike} 的波动率插值失败: {str(e)}")

                    # 然后再进行到期日之间的插值
                    for underlying, type_data in fitted_results.items():
                        spline_results[underlying] = {}
                        for option_type, (_, ivs, spline) in type_data.items():
                            if not spline:
                                self.logger.info(f"{underlying} 拟合曲线为空")
                                continue

                            # 获取该组合的所有期权
                            group_options = [r for r in results.values() if r.underlying == underlying and
                                             r.option_type == option_type]

                            if not group_options:
                                continue

                            # 收集拟合数据点
                            strikes = []
                            moneyness = []
                            ivs_bid = []
                            ivs_ask = []

                            for result in group_options:
                                if result.bid_iv > 0 and result.ask_iv > 0:
                                    strikes.append(result.strike_price)
                                    moneyness.append(result.moneyness)
                                    ivs_bid.append(float(result.bid_iv))
                                    ivs_ask.append(float(result.ask_iv))

                            # 拟合bid
                            bid_points = [MoneynessFitPoint(s, m, iv) for s, m, iv in
                                          zip(strikes, moneyness, ivs_bid)]
                            bid_fit = self._spline_fitter.fit_iv_curve(bid_points, self.min_points_for_fit)

                            # 拟合ask
                            ask_points = [MoneynessFitPoint(s, m, iv) for s, m, iv in
                                          zip(strikes, moneyness, ivs_ask)]
                            ask_fit = self._spline_fitter.fit_iv_curve(ask_points, self.min_points_for_fit)

                            # 创建拟合结果对象
                            fit_result = SplineFitResult(
                                underlying=underlying,
                                option_type=option_type,
                                strikes=strikes,
                                moneyness=moneyness,
                                ivs=ivs,
                                ivs_bid=ivs_bid,
                                ivs_ask=ivs_ask,
                                spline=spline,
                                spline_bid=bid_fit[0] if bid_fit else None,
                                spline_ask=ask_fit[0] if ask_fit else None
                            )

                            # 为每个插值到期日创建数据
                            for exp_date in interpolated_expiries:
                                # 计算剩余期限
                                T = calculate_time_to_expiry(exp_date, self.trading_dates, intraday_minutes)
                                if T is None or T <= 0:
                                    continue
                                T = float(T)

                                # 找到目标时间的前后两个到期日
                                next_idx = 0
                                for i, date in enumerate(expiry_dates):
                                    curve_t = float(
                                        calculate_time_to_expiry(date, self.trading_dates, intraday_minutes))
                                    if curve_t > T:
                                        next_idx = i
                                        break

                                # 获取前后两个到期日的时间信息
                                if next_idx == 0 or next_idx >= len(expiry_dates):
                                    # 如果目标时间在最近或最远到期日之外, 使用单一曲线
                                    prev_date = next_date = expiry_dates[0 if next_idx == 0 else -1]
                                    prev_t = next_t = float(calculate_time_to_expiry(prev_date, self.trading_dates,
                                                                                     intraday_minutes))
                                else:
                                    # 获取前后两个到期日
                                    prev_date = expiry_dates[next_idx - 1]
                                    next_date = expiry_dates[next_idx]
                                    prev_t = float(calculate_time_to_expiry(prev_date, self.trading_dates,
                                                                            intraday_minutes))
                                    next_t = float(calculate_time_to_expiry(next_date, self.trading_dates,
                                                                            intraday_minutes))

                                if prev_t is None or next_t is None:
                                    self.logger.error("计算相邻到期日的剩余期限失败")
                                    continue

                                # 为每个执行价计算插值
                                for strike in interpolated_strikes:
                                    if (strike not in expiry_iv_data[prev_date][option_type] or
                                            strike not in expiry_iv_data[next_date][option_type]):
                                        # 如果数据不存在, 跳过
                                        continue

                                    moneyness = strike / group_options[0].underlying_price

                                    try:
                                        # 获取前后到期日的波动率值
                                        prev_iv = expiry_iv_data[prev_date][option_type][strike]['iv']
                                        next_iv = expiry_iv_data[next_date][option_type][strike]['iv']

                                        # 在到期日之间进行线性插值
                                        # 检查时间差是否太小（避免除零）
                                        time_diff = next_t - prev_t
                                        if abs(time_diff) < 1e-4:  # 如果时间差小于阈值
                                            fitted_iv = prev_iv  # 直接使用前一个到期日的波动率
                                        else:
                                            weight = (T - prev_t) / time_diff
                                            fitted_iv = prev_iv + weight * (next_iv - prev_iv)
                                        fitted_iv = max(0.001, min(3.0, fitted_iv))

                                        # 计算bid/ask插值
                                        bid_iv = self._interpolate_volatility(
                                            moneyness, T, type_data, option_type, prev_t, next_t, next_idx
                                        )
                                        ask_iv = self._interpolate_volatility(
                                            moneyness, T, type_data, option_type, prev_t, next_t, next_idx
                                        )

                                        # 计算波动率差异标准差
                                        vol_std = self._get_volatility_std(
                                            product_code.upper(), exp_date, T, prev_t, next_t
                                        )

                                        # 创建插值点对象
                                        interpolated_point = InterpolatedIVPoint(
                                            strike_price=strike,
                                            moneyness=moneyness,
                                            remain_time=T * 244.0,
                                            fitted_iv=fitted_iv,
                                            bid_iv=bid_iv,
                                            ask_iv=ask_iv,
                                            vol_std=vol_std,
                                            expire_date=exp_date,
                                            underlying_price=group_options[0].underlying_price
                                        )

                                        # 创建复合键
                                        point_key = f"{strike}_{exp_date}"

                                        # 存储插值点到拟合结果对象中
                                        fit_result.interpolated_points[point_key] = interpolated_point

                                    except Exception as e:
                                        self.logger.error(f"插值计算出错: {str(e)}")

                            # 存储拟合结果
                            spline_results[underlying][option_type] = fit_result

                    # 更新结果
                    with self._lock:
                        self.spline_results[product_code] = spline_results

                    spline_duration = time.time() - spline_start_time
                    self.logger.info(f"品种 {product_code} 的样条拟合完成, 耗时 {spline_duration:.2f} 秒")

                except Exception as e:
                    self.logger.error(f"样条拟合和插值出错: {str(e)}")
                    self.logger.error(traceback.format_exc())

            # 更新结果
            with self._lock:
                self.iv_results[product_code] = results

            end_time = time.time()
            duration = end_time - start_time
            self.logger.info(
                f"品种 {product_code} 计算完成: "
                f"总计 {total_count} 个合约, "
                f"成功 {success_count} 个, "
                f"失败 {error_count} 个, "
                f"耗时 {duration:.2f} 秒"
            )

        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            self.logger.error(f"品种 {product_code} 的计算出错: {str(e)}, 耗时 {duration:.2f} 秒")

    def start_calculation(self, product_code: str) -> None:
        """启动品种的隐含波动率计算"""
        if product_code not in self.calculation_threads:
            stop_event = threading.Event()
            self.calculation_threads[product_code] = stop_event
            self.executor.submit(self.calculate_product_iv, product_code, stop_event)
            self.logger.info(f"启动品种 {product_code} 的隐含波动率计算")

    def stop_calculation(self, product_code: str) -> None:
        """停止品种的隐含波动率计算"""
        if product_code in self.calculation_threads:
            self.calculation_threads[product_code].set()
            del self.calculation_threads[product_code]
            self.logger.info(f"停止品种 {product_code} 的隐含波动率计算")

    def get_iv_results(self, product_code: str) -> Optional[Dict[str, IVResult]]:
        """获取品种的隐含波动率计算结果"""
        return self.iv_results.get(product_code)

    def start_atm_calculation(self) -> None:
        """启动ATM期权隐含波动率计算"""
        if not self.atm_iv_running:
            self.atm_iv_running = True
            self.atm_iv_thread = threading.Thread(target=self._calculate_all_atm_ivs, daemon=True)
            self.atm_iv_thread.start()
            self.logger.info("启动ATM期权隐含波动率计算")

    def stop_atm_calculation(self) -> None:
        """停止ATM期权隐含波动率计算"""
        self.atm_iv_running = False
        if self.atm_iv_thread and self.atm_iv_thread.is_alive():
            self.atm_iv_thread.join(timeout=5)  # 等待最多5秒
            self.logger.info("停止ATM期权隐含波动率计算")

    def _calculate_all_atm_ivs(self) -> None:
        """计算所有ATM期权的隐含波动率"""
        self.logger.info("开始计算所有ATM期权的隐含波动率")
        scheduler = sched.scheduler(time.time, time.sleep)
        scheduler_task = None  # 用于存储下一个调度任务

        def calculate_once():
            """执行一次计算"""
            nonlocal scheduler_task
            if not self.atm_iv_running:
                # 如果有待执行的调度任务, 取消它
                if scheduler_task:
                    try:
                        scheduler.cancel(scheduler_task)
                    except ValueError:
                        pass  # 任务可能已经执行或取消
                return

            current_time = time.time()
            try:
                # 获取所有品种
                all_products = self.contract_manager.get_all_product_codes()
                self.logger.debug(f"开始计算ATM IV, 品种数量: {len(all_products)}")

                # 存储ATM IV结果
                atm_results = {}

                for product_code in all_products:
                    # 获取日内分钟数
                    intraday_minutes = self.contract_info.loc[product_code.upper(), 'trade_minutes']
                    # 如果日内分钟数小于300分钟, 并且当前时间不在交易时间内, 则跳过计算
                    now_hour = time.localtime().tm_hour
                    if intraday_minutes < 300 and (now_hour > 17 or now_hour < 8):
                        continue

                    # 获取该品种的所有合约信息
                    contracts = self.contract_manager.get_product_contracts(product_code)
                    if not contracts:
                        self.logger.debug(f"未找到品种 {product_code} 的合约信息")
                        continue

                    # 获取所有期货合约
                    futures = contracts.get('futures', {})
                    self.logger.debug(f"品种 {product_code} 的期货合约数量: {len(futures)}")

                    for future_id in futures:
                        self.logger.debug(f"处理期货合约: {future_id}")
                        # 查找对应的ATM期权
                        atm_option = self._find_atm_option(future_id)
                        if not atm_option:
                            continue

                        # 获取期权合约的市场数据
                        option_data = self.market_data_manager.get_market_data(atm_option)
                        if not option_data:
                            self.logger.debug(f"未找到期权 {atm_option} 的市场数据")
                            continue

                        # 获取标的合约的市场数据
                        underlying_data = self.market_data_manager.get_market_data(future_id)
                        if not underlying_data:
                            self.logger.debug(f"未找到标的 {future_id} 的市场数据")
                            continue

                        # 计算中间价
                        underlying_mid_price = (underlying_data.bid_price + underlying_data.ask_price) * 0.5
                        option_mid_price = (option_data.bid_price + option_data.ask_price) * 0.5
                        self.logger.debug(f"标的中间价: {underlying_mid_price}, 期权中间价: {option_mid_price}")

                        # 获取期权信息
                        option_info = None
                        for opt_type in ['call', 'put']:
                            for strikes in contracts['options'][opt_type].values():
                                if atm_option in strikes:
                                    option_info = strikes[atm_option]
                                    break
                            if option_info:
                                break

                        if not option_info:
                            self.logger.debug(f"未找到期权 {atm_option} 的合约信息")
                            continue

                        # 计算剩余期限
                        T = calculate_time_to_expiry(option_info.expire_date, self.trading_dates, intraday_minutes)
                        if T is None or T <= 0:
                            self.logger.debug(f"期权 {atm_option} 的剩余期限无效: {T}")
                            continue
                        T = float(T)

                        # 计算隐含波动率
                        try:
                            iv = self.calculate_iv(
                                underlying_mid_price,
                                option_info.strike_price,
                                T,
                                0.0,
                                option_mid_price,
                                option_info.option_type
                            )
                            if iv > 0:
                                atm_results[future_id] = (atm_option, iv)
                                self.logger.debug(f"成功计算期权 {atm_option} 的隐含波动率: {iv:.4f}")
                            else:
                                self.logger.debug(f"期权 {atm_option} 计算得到的隐含波动率无效: {iv}")
                        except Exception as e:
                            self.logger.error(f"计算ATM期权 {atm_option} 的隐含波动率时出错: {str(e)}")

                # 更新结果
                with self._lock:
                    self.atm_iv_results = atm_results
                    self.logger.debug(f"完成ATM IV计算, 成功计算 {len(atm_results)} 个品种")

            except Exception as e:
                self.logger.error(f"计算ATM期权隐含波动率时出错: {str(e)}\n{traceback.format_exc()}")

            finally:
                # 如果还在运行, 安排下一次计算
                if self.atm_iv_running:
                    # 计算下一个30秒的整点时间
                    next_time = (current_time // 30 + 1) * 30
                    scheduler_task = scheduler.enterabs(next_time, 1, calculate_once)

        # 计算第一次执行的时间（对齐到下一个30秒的整点）
        current_time = time.time()
        next_time = (current_time // 30 + 1) * 30

        # 安排第一次计算
        scheduler_task = scheduler.enterabs(next_time, 1, calculate_once)

        # 修改调度器循环
        while self.atm_iv_running:
            try:
                scheduler.run(blocking=False)
                time.sleep(0.1)
            except Exception as e:
                self.logger.error(f"ATM IV计算调度器运行出错: {str(e)}")
                if self.atm_iv_running:
                    time.sleep(1)

        self.logger.info("ATM期权隐含波动率计算已停止")

    def get_atm_iv_result(self, contract: str) -> Tuple[Optional[str], Optional[float]]:
        """获取具体合约的平值期权隐含波动率计算结果"""
        if contract in self.atm_iv_results:
            return self.atm_iv_results.get(contract)
        return None, None

    def _find_atm_option(self, contract: str) -> Optional[str]:
        """查找平值期权

        根据给定的合约代码查找对应的平值期权。平值期权是指执行价格与标的价格比值(moneyness)最接近1的期权。

        Args:
            contract: 期货合约代码, 例如 "m2405"

        Returns:
            Optional[str]: 返回平值期权合约代码, 如果找到则返回合约代码, 否则返回None

        Raises:
            Exception: 在查找过程中发生错误时抛出异常
        """
        try:
            match = re.search(r'([a-zA-Z]+)\d+', contract)
            if not match:
                self.logger.debug(f"未找到品种代码: {contract}")
                return None
            product_code = match.group(1)
            # 查找moneyness最接近1的期权
            atm_option = None

            underlying_data = self.market_data_manager.get_market_data(contract)
            if not underlying_data:
                self.logger.debug(f"未找到标的合约 {contract} 的市场数据")
                return None

            # 计算标的中间价
            if underlying_data.bid_price == 0. or underlying_data.ask_price == 0.:
                underlying_mid_price = underlying_data.last_price
            else:
                underlying_mid_price = (underlying_data.bid_price + underlying_data.ask_price) * 0.5
            self.logger.debug(f"标的合约 {contract} 的中间价: {underlying_mid_price}")

            all_options = self.contract_manager.get_product_contracts(product_code)['options']['call']
            all_strikes = np.sort(np.array(list(all_options.keys())))
            self.logger.debug(f"品种 {product_code} 的看涨期权执行价列表: {all_strikes}")

            # 计算每个执行价格的moneyness
            moneyness = all_strikes / underlying_mid_price
            self.logger.debug(f"计算的moneyness列表: {moneyness}")

            # 找到moneyness最接近1的执行价格
            atm_strike = all_strikes[np.argmin(np.abs(moneyness - 1.0))]
            self.logger.debug(f"找到最接近平值的执行价: {atm_strike}, moneyness: {atm_strike / underlying_mid_price}")

            # 获取该执行价格下的所有期权合约
            options = all_options[atm_strike]
            self.logger.debug(f"该执行价下的期权合约数量: {len(options)}")

            # 找到与期货合约月份相同的期权
            for option_id, option_info in options.items():
                if option_info.underlying == contract:
                    atm_option = option_id
                    self.logger.debug(f"找到平值期权: {atm_option}, 标的: {contract}, 执行价: {atm_strike}")
                    break

            if not atm_option:
                self.logger.debug(f"未找到与期货合约 {contract} 匹配的平值期权")

            return atm_option

        except Exception as e:
            self.logger.error(f"查找平值期权失败: {str(e)}\n{traceback.format_exc()}")
            return None

    def get_spline_results(self, product_code: str) -> Optional[Dict[str, Dict[str, SplineFitResult]]]:
        """获取样条拟合结果"""
        return self.spline_results.get(product_code)

    def _cleanup_loop(self) -> None:
        """清理不活跃的计算任务"""
        while True:
            try:
                active_products = self.subscription_manager.get_active_products()
                current_products = set(self.calculation_threads.keys())

                # 停止不再活跃的品种的计算
                for product in current_products - active_products:
                    self.stop_calculation(product)

                # 启动新活跃的品种的计算
                for product in active_products - current_products:
                    self.start_calculation(product)

            except Exception as e:
                self.logger.error(f"清理任务出错: {str(e)}")

            time.sleep(5)  # 每5秒检查一次

    def shutdown(self) -> None:
        """关闭计算器"""
        # 停止所有计算
        for product_code in list(self.calculation_threads.keys()):
            self.stop_calculation(product_code)

        # 停止ATM IV计算
        self.stop_atm_calculation()

        # 关闭线程池
        self.executor.shutdown(wait=True)

    def reload_trading_dates(self, trading_dates_file: Optional[str] = None) -> None:
        """重新加载交易日历

        Args:
            trading_dates_file: 交易日历文件路径, 如果为None则使用当前文件
        """
        try:
            if trading_dates_file:
                self.trading_dates = pd.read_csv(trading_dates_file)
                self.logger.info(f"成功重新加载交易日历文件: {trading_dates_file}")
            else:
                self.trading_dates = pd.read_csv(self.trading_dates_file)
                self.logger.info("成功重新加载当前交易日历文件")
        except Exception as e:
            self.logger.error(f"重新加载交易日历文件失败: {str(e)}")
            raise

    def get_quote_recommendation(self, product_code: str, expire_date: str, strike_price: float,
                                 underlying_price: float, option_type: str = 'call') -> Optional[Dict]:
        """
        获取期权报价推荐

        Args:
            product_code: 期货品种代码
            expire_date: 到期日期(YYYY-MM-DD)
            strike_price: 行权价
            underlying_price: 标的价格
            option_type: 期权类型('call'或'put')

        Returns:
            报价推荐结果
        """
        try:
            # 获取当前的波动率曲面数据
            spline_results = self.spline_results.get(product_code)
            if not spline_results:
                self.logger.error(f"未找到品种 {product_code} 的波动率曲面数据")
                return None

            # 转换为波动率曲线数据格式
            iv_curves = {}
            for underlying, type_data in spline_results.items():
                iv_expiry_date = self.contract_manager.get_option_underlying_expire_date(underlying)
                iv_curves[iv_expiry_date] = {
                    'call': (
                        type_data['call'].strikes,
                        type_data['call'].ivs,
                        type_data['call'].spline,
                        type_data['call'].ivs_bid,
                        type_data['call'].ivs_ask,
                        type_data['call'].spline_bid,
                        type_data['call'].spline_ask
                    ) if 'call' in type_data else ([], [], None, [], [], None, None),
                    'put': (
                        type_data['put'].strikes,
                        type_data['put'].ivs,
                        type_data['put'].spline,
                        type_data['put'].ivs_bid,
                        type_data['put'].ivs_ask,
                        type_data['put'].spline_bid,
                        type_data['put'].spline_ask
                    ) if 'put' in type_data else ([], [], None, [], [], None, None),
                }

            # 获取报价推荐
            return self.quote_recommender.get_quote_recommendation(
                product_code=product_code,
                expire_date=expire_date,
                strike_price=strike_price,
                underlying_price=underlying_price,
                iv_curves=iv_curves,
                option_type=option_type
            )

        except Exception as e:
            self.logger.error(f"获取报价推荐失败: {str(e)}")
            return None

    def _interpolate_volatility(self, moneyness: float, target_time: float, type_data: Dict, option_type: str,
                                prev_t: float, next_t: float, next_idx: int) -> Optional[float]:
        """
        在波动率曲线上进行插值

        Args:
            moneyness: 虚实度
            target_time: 目标剩余期限
            type_data: 期权类型数据
            option_type: 期权类型
            prev_t: 前一个到期日的剩余期限
            next_t: 后一个到期日的剩余期限
            next_idx: 后一个到期日的索引

        Returns:
            插值后的波动率
        """
        try:
            if next_idx == 0:
                # 目标时间在最近到期日之前
                return float(type_data[option_type][2](moneyness))

            if next_idx >= len(type_data[option_type]):
                # 目标时间在最远到期日之后
                return float(type_data[option_type][2](moneyness))

            # 在两个到期日之间进行线性插值
            prev_vol = float(type_data[option_type][2](moneyness))
            next_vol = float(type_data[option_type][2](moneyness))

            # 检查时间差是否太小（避免除零）
            time_diff = next_t - prev_t
            if abs(time_diff) < 1e-4:  # 如果时间差小于阈值
                result = prev_vol  # 直接使用前一个到期日的波动率
            else:
                weight = (target_time - prev_t) / time_diff
                result = prev_vol + weight * (next_vol - prev_vol)

            return max(0.001, min(3.0, result))  # 限制波动率范围

        except Exception as e:
            self.logger.error(f"波动率插值失败: {str(e)}")
            return None

    def _get_volatility_std(self, pdt_upper: str, target_date: str, target_time: float, prev_t: float, next_t: float) -> \
            Optional[float]:
        """
        获取对应期限的波动率差异标准差

        Args:
            pdt_upper: 大写的品种代码
            target_date: 目标到期日
            target_time: 目标剩余期限
            prev_t: 前一个到期日的剩余期限
            next_t: 后一个到期日的剩余期限

        Returns:
            波动率差异标准差
        """
        try:
            if pdt_upper not in self.vol_diff_cont_months:
                self.logger.warning(f"品种{pdt_upper}没有波动率差异数据")
                return None

            months = self.vol_diff_cont_months[pdt_upper]

            # 找到目标日期在月份列表中的位置
            target_month = target_date[5:7]  # 提取月份
            month_idx = 0
            for i, month in enumerate(months):
                if int(month) > int(target_month):
                    month_idx = i
                    break

            if month_idx == 0:
                if (pdt_upper, months[0], months[1]) in self.vol_diff_data:
                    return self.vol_diff_data[(pdt_upper, months[0], months[1])]  # noqa

            if month_idx >= len(months) - 1:
                if (pdt_upper, months[-2], months[-1]) in self.vol_diff_data:
                    return self.vol_diff_data[(pdt_upper, months[-2], months[-1])]  # noqa

            # 在相邻月份之间进行线性插值
            prev_std = float(self.vol_diff_data[(pdt_upper, months[month_idx - 1], months[month_idx])])  # noqa
            next_std = float(self.vol_diff_data[(pdt_upper, months[month_idx], months[month_idx + 1])])  # noqa

            # 检查时间差是否太小（避免除零）
            time_diff = next_t - prev_t
            if abs(time_diff) < 1e-4:  # 如果时间差小于阈值
                result = prev_std  # 直接使用前一个月份的标准差
            else:
                weight = (target_time - prev_t) / time_diff
                result = prev_std + weight * (next_std - prev_std)

            return float(max(0.0001, result))  # 确保标准差为正

        except Exception as e:
            self.logger.error(f"获取波动率标准差失败: {str(e)}")
            return None

    def get_vol_diff_data(self) -> Dict:
        """获取波动率差异数据"""
        return self.vol_diff_data

    def get_vol_diff_cont_months(self) -> Dict:
        """获取连续月份数据"""
        return self.vol_diff_cont_months
