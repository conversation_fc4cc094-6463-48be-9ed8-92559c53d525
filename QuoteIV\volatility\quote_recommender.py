#!/usr/bin/python
# -*- coding:utf-8 -*-
# Author: ltw
# Time: 2025-01-30

import os
import re
from typing import Optional, Dict, List

import numpy as np
import pandas as pd

from volatility.logger_utils import ErrorLoggerAdapter
from volatility.time_utils import calculate_time_to_expiry


class QuoteRecommender:
    """期权报价推荐器"""

    def __init__(self, logger: ErrorLoggerAdapter, trading_dates_file: str = 'trading_date.csv',
                 contract_info_file: str = 'contract_info.csv', option_info_file: str = 'option_infos.csv',
                 vol_diff_data: Optional[Dict] = None, vol_diff_cont_months: Optional[Dict] = None,
                 vol_newest: Optional[Dict] = None) -> None:
        """
        初始化报价推荐器
        
        Args:
            logger: 日志记录器
            trading_dates_file: 交易日历文件路径
            contract_info_file: 合约信息文件路径
            vol_diff_data: 波动率差异数据(从IVCalculator获取)
            vol_diff_cont_months: 连续月份数据(从IVCalculator获取)
        """
        self.logger = logger
        self.trading_dates_file = trading_dates_file

        # 读取交易日历
        try:
            self.trading_dates = pd.read_csv(os.path.join(os.getcwd(), '..', trading_dates_file), parse_dates=[0])
            self.logger.info(f"成功读取交易日历文件: {trading_dates_file}")
        except Exception as e:
            raise Exception(f"读取交易日历文件失败: {str(e)}")

        # 读取合约信息
        try:
            self.contract_info = pd.read_csv(os.path.join(os.getcwd(), '..', contract_info_file), index_col=0)
            self.logger.info(f"成功读取合约信息文件: {contract_info_file}")
        except Exception as e:
            raise Exception(f"读取合约信息文件失败: {str(e)}")

        # 读取期权到期信息
        try:
            self.option_info = pd.read_csv(os.path.join(os.getcwd(), '../historical_data', option_info_file),
                                           index_col=0)
            self.option_info = self.option_info[['underlying', 'expire_date']].groupby('underlying').last()
            self.option_info.reset_index(inplace=True)
            self.option_info['product'] = self.option_info['underlying'].apply(lambda x: re.sub('\d+', '', x).upper())
            self.logger.info(f"成功读取期权到期信息文件: {option_info_file}")
        except Exception as e:
            raise Exception(f"读取期权到期信息文件失败: {str(e)}")

        # 使用传入的波动率差异数据
        self.vol_diff_data = vol_diff_data or {}
        self.vol_diff_cont_months = vol_diff_cont_months or {}
        self.vol_newest = vol_newest or {}
        if vol_diff_data and vol_diff_cont_months and vol_newest:
            self.logger.info("成功接收波动率差异统计数据")

    def reload_data(self, vol_diff_data: Optional[Dict] = None, vol_diff_cont_months: Optional[Dict] = None,
                    vol_newest: Optional[Dict] = None):
        """重新加载波动率差异数据"""
        self.logger.info("重新加载波动率差异数据")
        self.vol_diff_data = vol_diff_data or {}
        self.vol_diff_cont_months = vol_diff_cont_months or {}
        self.vol_newest = vol_newest or {}

    def _get_stats_table_for_pair(self, contract1: str, contract2: str) -> list:
        """
        获取两个合约在所有周期下的最新波动率和差异统计
        返回: List[dict], 每个dict含 period, vol1_last, vol2_last
        """
        stats_table = []
        # 1. 获取所有周期
        if not hasattr(self, 'vol_newest'):
            self.logger.warning("未加载历史波动率")
            return stats_table

        for period, vdf in self.vol_newest.items():
            # 找到合约1和合约2的列
            col1 = [c for c in vdf.columns if contract1 in c]
            col2 = [c for c in vdf.columns if contract2 in c]
            vol1_last = float(vdf[col1[0]].iloc[-1]) if col1 else None
            vol2_last = float(vdf[col2[0]].iloc[-1]) if col2 else None

            stats_table.append({
                "period": period,
                "vol1_last": vol1_last,
                "vol2_last": vol2_last
            })
        return stats_table

    def calculate_vol_std_from_stats(self, stats_table):
        # stats_table: List[Dict], 每个dict有'vol1_last'和'vol2_last'
        means = []
        for stat in stats_table:
            try:
                v1 = float(stat['vol1_last'])
                v2 = float(stat['vol2_last'])
                means.append(v1 - v2)
            except Exception:
                continue
        if not means:
            return 0.0
        mean_avg = np.mean(means)
        std = np.sqrt(np.mean([(x - mean_avg) ** 2 for x in means]))  # noqa
        return std

    def get_quote_recommendation(self, product_code: str, expire_date: str, strike_price: float,
                                 underlying_price: float, iv_curves: Dict, option_type: str = 'call') -> Optional[Dict]:
        """
        获取期权报价推荐
        
        Args:
            product_code: 期货品种代码
            expire_date: 到期日期(YYYY-MM-DD)或合约代码
            strike_price: 行权价
            underlying_price: 标的价格
            iv_curves: 波动率曲线数据
            option_type: 期权类型('call'或'put')
            
        Returns:
            报价推荐结果字典, 包含:
            - moneyness: 虚实度
            - time_to_expiry: 剩余期限
            - base_mid_vol: 基准波动率
            - recommend_bid_vol: 买入报价波动率
            - recommend_ask_vol: 卖出报价波动率
            - std_1: 一倍标准差
            - std_2: 两倍标准差
        """
        try:
            # 1. 计算虚实度和剩余期限
            moneyness = strike_price / underlying_price
            self.logger.debug(f"计算虚实度: K/S = {moneyness:.4f}")

            # 获取品种的日内交易分钟数
            intraday_minutes = self.contract_info.loc[product_code.upper(), 'trade_minutes']

            # 计算剩余期限
            time_to_expiry = calculate_time_to_expiry(expire_date, self.trading_dates, intraday_minutes)

            if time_to_expiry is None:
                self.logger.error(f"计算剩余期限失败, 到期日: {expire_date}")
                return None

            self.logger.debug(f"计算剩余期限: {time_to_expiry:.4f}年")

            # 找到最近的两个到期日
            expiry_dates = sorted(iv_curves.keys())
            if not expiry_dates:
                self.logger.warning("没有可用的波动率曲线数据")
                return None

            pdt_upper = product_code.upper()

            # 如果只有一个到期日, 直接使用该曲线
            if len(expiry_dates) == 1:
                curve = iv_curves[expiry_dates[0]][option_type]
                if curve[2] is None:  # 如果没有拟合曲线
                    self.logger.warning("波动率曲线未拟合")
                    return None
                base_mid_vol = float(curve[2](moneyness))
                base_bid_vol = float(curve[5](moneyness)) if curve[5] is not None else None
                base_ask_vol = float(curve[6](moneyness)) if curve[6] is not None else None
                vol_std = 0.0
                if pdt_upper in self.vol_diff_cont_months:
                    months = self.vol_diff_cont_months[pdt_upper]
                    if (pdt_upper, months[0], months[1]) in self.vol_diff_data:
                        vol_std = self.vol_diff_data[(pdt_upper, months[0], months[1])]
            else:
                # 找到目标到期时间的前后两个到期日
                next_idx = 0
                for i, date in enumerate(expiry_dates):
                    curve_t = calculate_time_to_expiry(date, self.trading_dates, intraday_minutes)
                    if curve_t > time_to_expiry:
                        next_idx = i
                        break

                # 获取前后两个到期日的波动率
                prev_date = expiry_dates[next_idx - 1]
                next_date = expiry_dates[next_idx]

                prev_t = calculate_time_to_expiry(prev_date, self.trading_dates, intraday_minutes)
                next_t = calculate_time_to_expiry(next_date, self.trading_dates, intraday_minutes)

                if prev_t is None or next_t is None:
                    self.logger.error("计算相邻到期日的剩余期限失败")
                    return None

                # 2. 在波动率曲线上进行插值
                vols = self._interpolate_volatility(moneyness, time_to_expiry, expiry_dates, next_idx, iv_curves,
                                                    option_type, prev_date, next_date, prev_t, next_t)
                base_mid_vol = vols['mid']
                base_bid_vol = vols['bid']
                base_ask_vol = vols['ask']

                if base_mid_vol is None:
                    self.logger.error("波动率插值失败")
                    return None

                self.logger.debug(f"基准波动率: {base_mid_vol:.4f}")

                # 3. 获取波动率差异的标准差
                # vol_std = self._get_volatility_std(pdt_upper, expiry_dates, next_idx, time_to_expiry, prev_t, next_t)
                contract1 = self._find_contract_by_expire_date(product_code, prev_date)
                contract2 = self._find_contract_by_expire_date(product_code, next_date)
                if not contract1 or not contract2:
                    self.logger.error(f"找不到与到期日对应的合约: {prev_date}, {next_date}")
                    return None
                stats_table = self._get_stats_table_for_pair(contract1, contract2)
                vol_std = self.calculate_vol_std_from_stats(stats_table)

                if vol_std is None:
                    self.logger.error("获取波动率标准差失败")
                    return None

                self.logger.debug(f"波动率标准差: {vol_std:.4f}")

            # 4. 计算报价波动率
            std_1 = vol_std
            std_2 = 2 * vol_std

            recommend_bid_vol = base_mid_vol - std_1
            recommend_ask_vol = base_mid_vol + std_1

            result = {
                'moneyness': moneyness,
                'time_to_expiry': time_to_expiry,
                'base_mid_vol': base_mid_vol,
                'base_bid_vol': base_bid_vol,
                'base_ask_vol': base_ask_vol,
                'recommend_bid_vol': max(0.001, recommend_bid_vol),  # 确保波动率不小于0.1%
                'recommend_ask_vol': min(3.0, recommend_ask_vol),  # 确保波动率不超过500%
                'std_1': std_1,
                'std_2': std_2
            }

            self.logger.info(f"生成报价推荐: {result}")
            return result

        except Exception as e:
            self.logger.error(f"获取报价推荐失败: {str(e)}")
            return None

    def _interpolate_volatility(self, moneyness: float, time_to_expiry: float, expiry_dates: List[str], next_idx: int,
                                iv_curves: Dict, option_type: str, prev_date: str, next_date: str, prev_t: float,
                                next_t: float) -> dict:
        """
        返回 {'mid': ..., 'bid': ..., 'ask': ...}
        """
        try:
            def get_spline(date, kind):
                curve = iv_curves[date][option_type]
                if kind == 'mid':
                    return curve[2]
                elif kind == 'bid':
                    return curve[5]
                elif kind == 'ask':
                    return curve[6]
                else:
                    return None

            result = {}
            for kind in ['mid', 'bid', 'ask']:
                if next_idx == 0:
                    spline = get_spline(expiry_dates[0], kind)
                    result[kind] = float(spline(moneyness)) if spline else None
                elif next_idx >= len(expiry_dates):
                    spline = get_spline(expiry_dates[-1], kind)
                    result[kind] = float(spline(moneyness)) if spline else None
                else:
                    prev_spline = get_spline(prev_date, kind)
                    next_spline = get_spline(next_date, kind)
                    if prev_spline is None or next_spline is None:
                        result[kind] = None
                    else:
                        prev_vol = float(prev_spline(moneyness))
                        next_vol = float(next_spline(moneyness))
                        weight = (time_to_expiry - prev_t) / (next_t - prev_t)
                        result[kind] = prev_vol + weight * (next_vol - prev_vol)
            return result
        except Exception as e:
            self.logger.error(f"波动率插值失败: {str(e)}")
            return {'mid': None, 'bid': None, 'ask': None}

    def _get_volatility_std(self, pdt_upper: str, expiry_dates: List[str], next_idx: int, time_to_expiry: float,
                            prev_t: float, next_t: float) -> Optional[float]:
        """获取对应期限的波动率差异标准差"""
        try:
            if pdt_upper not in self.vol_diff_cont_months:
                self.logger.warning(f"品种{pdt_upper}没有波动率差异数据")
                return None

            months = self.vol_diff_cont_months[pdt_upper]

            if next_idx == 0:
                if (pdt_upper, months[0], months[1]) in self.vol_diff_data:
                    return self.vol_diff_data[(pdt_upper, months[0], months[1])]  # noqa

            if next_idx >= len(expiry_dates) - 1:
                if (pdt_upper, months[-2], months[-1]) in self.vol_diff_data:
                    return self.vol_diff_data[(pdt_upper, months[-2], months[-1])]  # noqa

            # 在两个到期日之间进行线性插值
            prev_std = float(self.vol_diff_data[(pdt_upper, months[next_idx - 1], months[next_idx])])  # noqa
            next_std = float(self.vol_diff_data[(pdt_upper, months[next_idx], months[next_idx + 1])])  # noqa

            weight = (time_to_expiry - prev_t) / (next_t - prev_t)
            result = prev_std + weight * (next_std - prev_std)
            self.logger.debug(f"波动率插值结果: {result:.4f} (weight={weight:.4f})")
            return result

        except Exception as e:
            self.logger.error(f"获取波动率标准差失败: {str(e)}")
            return None

    def _find_contract_by_expire_date(self, product_code: str, expire_date: str) -> Optional[str]:
        """
        根据品种和到期日查找合约名
        """
        # expire_date 可能是 "2024-06-20" 或 "20240620"
        # 先尝试标准化格式
        expire_date_str = expire_date.replace('-', '')
        df = self.option_info
        # 过滤品种
        df = df.loc[df['product'] == product_code.upper()]
        # 过滤到期日
        row = df[(df['expire_date'].astype(str) == expire_date_str)]
        if not row.empty:
            return row.iloc[0]['underlying']
        # 如果没找到，再尝试其他格式
        row = df[(df['expire_date'].astype(str) == expire_date)]
        if not row.empty:
            return row.iloc[0]['underlying']
        return None
