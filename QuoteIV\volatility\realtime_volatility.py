#!/usr/bin/python
# -*- coding:utf-8 -*-
# Author: ltw
# Time: 2025-01-26

import argparse
import asyncio
import logging
import msvcrt
import os
import re
import sys
import time
import traceback

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from ronhon import mdapi, tdapi, CFtdcMdSpi, CTradeSpi
from volatility.iv_manager import ContractManager, ContractInfo, MarketDataManager, SubscriptionManager
from volatility.iv_calculator import IVCalculator
from volatility.websocket_server import WebSocketServer
from volatility.logger_utils import ErrorLoggerAdapter
from config import config


def convert_to_contract_info(contract_id: str, contract_dict: dict) -> ContractInfo | None:
    # 从合约代码中提取品种代码
    if 'underlying' in contract_dict:
        product_distill = contract_dict['underlying']
    else:
        product_distill = contract_id
    match = re.search(r'^([A-Za-z]+)', product_distill)
    if not match:
        return None
    product_code = match.group(1)

    return ContractInfo(
        contract_id=contract_id,
        product_code=product_code,
        expire_date=contract_dict['expire_date'],
        exchange=contract_dict['exchange'],
        strike_price=contract_dict.get('strike_price', 0.0),  # 期货合约没有这个字段
        option_type=contract_dict.get('option_type', ''),  # 期货合约没有这个字段
        underlying=contract_dict.get('underlying', ''),  # 期货合约没有这个字段
        product_class=contract_dict['product_class']
    )


def calculate_realtime_volatility():
    """实时波动率计算主函数"""
    # 配置logger
    logger = logging.getLogger('volatility')
    # logger.setLevel(logging.DEBUG)
    logger.propagate = False

    # 检查是否已经有处理器, 如果没有才添加
    if not logger.handlers:
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(filename)s-%(funcName)s-%(lineno)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)

        # 添加处理器到logger
        logger.addHandler(console_handler)

    logger = ErrorLoggerAdapter(logger, {})

    # 添加启动日志
    logger.info('程序启动')

    try:
        # 初始化管理器
        contract_manager = ContractManager(logger)
        market_data_manager = MarketDataManager(logger)
        subscription_manager = SubscriptionManager(logger)

        # 初始化计算器
        iv_calculator = IVCalculator(
            contract_manager=contract_manager,
            market_data_manager=market_data_manager,
            subscription_manager=subscription_manager,
            logger=logger,
            trading_dates_file='trading_date.csv',
            contract_info_file='contract_info.csv',
            option_info_file='option_infos.csv',
            active_contracts_file='active_contracts.json',  # 活跃合约配置文件
            enable_spline_fit=True,  # 启用样条拟合
            min_points_for_fit=4,  # 最小拟合点数
        )

        # 行情API配置
        ctp_cfg = config['ctp']
        mduser_api = mdapi.CThostFtdcMdApi.CreateFtdcMdApi('output/')
        mduser_spi = CFtdcMdSpi(mduser_api, logger, ctp_cfg['user_id'], ctp_cfg['password'], ctp_cfg['broker_id'],
                                market_data_manager)
        mduser_api.RegisterFront(ctp_cfg['md'])
        mduser_api.RegisterSpi(mduser_spi)
        mduser_api.Init()
        logger.info('行情API初始化完成')

        # 交易API配置
        trade_api = tdapi.CThostFtdcTraderApi.CreateFtdcTraderApi('output/')
        trade_spi = CTradeSpi(trade_api, mduser_api, mduser_spi, logger, ctp_cfg['user_id'], ctp_cfg['password'],
                              ctp_cfg['broker_id'], ctp_cfg['app_id'], ctp_cfg['authcode'])
        trade_api.RegisterSpi(trade_spi)
        trade_api.SubscribePrivateTopic(tdapi.THOST_TERT_QUICK)
        trade_api.SubscribePublicTopic(tdapi.THOST_TERT_QUICK)
        trade_api.RegisterFront(ctp_cfg['td'])
        trade_api.Init()
        logger.info('交易API初始化完成')

        # 等待查询完成
        wait_count = 0
        while wait_count < 10:
            if trade_spi.is_all_query_done:
                # 将合约信息添加到ContractManager
                for contract_id, contract_info in trade_spi.contracts.items():
                    if contract_id in trade_spi.all_conts:
                        contract_manager.add_contract(convert_to_contract_info(contract_id, contract_info))
                logger.info(f'已加载 {len(trade_spi.all_conts)} 个合约信息')
                break
            else:
                logger.debug('等待查询完成')
            time.sleep(1)
            wait_count += 1

        if not trade_spi.is_all_query_done:
            logger.info('合约查询超时, 直接订阅获取行情')
            trade_spi.after_query_contracts()
            for contract_id, contract_info in trade_spi.contracts.items():
                if contract_id in trade_spi.all_conts:
                    contract_manager.add_contract(convert_to_contract_info(contract_id, contract_info))
            logger.info(f'已加载 {len(trade_spi.all_conts)} 个合约信息')

        # 创建事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # 启动WebSocket服务器
        ws_server = WebSocketServer(iv_calculator, subscription_manager, logger, "0.0.0.0", 8765)
        ws_server_task = loop.create_task(ws_server.start())

        logger.info('按 C 键退出循环...')

        # 主循环
        while True:
            if msvcrt.kbhit():  # 检查是否有键盘输入
                key = msvcrt.getch().decode('utf-8').lower()
                if key == 'c':
                    logger.info('检测到退出指令, 正在退出...')
                    break

            # 让出控制权给事件循环
            loop.stop()
            loop.run_forever()
            time.sleep(0.1)  # 控制更新频率

        # 清理资源
        logger.info('开始清理资源...')

        # 先停止WebSocket服务器
        logger.info('停止WebSocket服务器...')
        try:
            loop.run_until_complete(ws_server.stop())
        except Exception as e:
            logger.error(f'停止WebSocket服务器时出错: {str(e)}')

        # 取消WebSocket服务器任务
        logger.info('取消WebSocket服务器任务...')
        ws_server_task.cancel()
        try:
            loop.run_until_complete(asyncio.gather(ws_server_task, return_exceptions=True))
        except asyncio.CancelledError:
            logger.info('WebSocket服务器任务已取消')
        except Exception as e:
            logger.error(f'取消WebSocket服务器任务时出错: {str(e)}')

        # 清理所有待处理的任务
        logger.info('清理待处理的任务...')
        pending = asyncio.all_tasks(loop)
        if pending:
            logger.info(f'发现 {len(pending)} 个待处理任务，正在取消...')
            for task in pending:
                task.cancel()
            try:
                loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
            except asyncio.CancelledError:
                logger.info('所有待处理任务已取消')
            except Exception as e:
                logger.error(f'取消待处理任务时出错: {str(e)}')

        # 停止事件循环
        logger.info('停止事件循环...')
        try:
            loop.stop()
            loop.run_forever()
            loop.close()
            logger.info('事件循环已关闭')
        except Exception as e:
            logger.error(f'关闭事件循环时出错: {str(e)}')

        # 关闭IV计算器
        logger.info('关闭IV计算器...')
        iv_calculator.shutdown()

        # 登出账户
        logger.info('账户登出...')
        mduser_spi.req_logout()
        trade_spi.req_logout()

        # 等待一小段时间让登出请求发送
        time.sleep(1)

        # 清理API资源
        logger.info('清理API资源...')
        mduser_api.Release()
        trade_api.Release()

        logger.info('所有资源已释放')

    except Exception as e:
        logger.error(f'程序异常: {str(e)}')
        logger.error(f'堆栈信息:\n{traceback.format_exc()}')


if __name__ == '__main__':
    # 创建 ArgumentParser 对象
    parser = argparse.ArgumentParser(description="Real-time Volatility Script")

    # 添加自定义参数
    parser.add_argument("--unique-identifier", action="store_true", help="Unique identifier for process management")

    # 解析命令行参数
    args = parser.parse_args()

    # 检查是否传递了 --unique-identifier 参数
    if args.unique_identifier:
        print("Running with unique identifier...")
    else:
        print("Running without unique identifier...")

    calculate_realtime_volatility()
